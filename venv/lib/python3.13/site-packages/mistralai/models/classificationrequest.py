"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
import pydantic
from typing import List, Union
from typing_extensions import Annotated, TypeAliasType, TypedDict


ClassificationRequestInputsTypedDict = TypeAliasType(
    "ClassificationRequestInputsTypedDict", Union[str, List[str]]
)
r"""Text to classify."""


ClassificationRequestInputs = TypeAliasType(
    "ClassificationRequestInputs", Union[str, List[str]]
)
r"""Text to classify."""


class ClassificationRequestTypedDict(TypedDict):
    model: str
    r"""ID of the model to use."""
    inputs: ClassificationRequestInputsTypedDict
    r"""Text to classify."""


class ClassificationRequest(BaseModel):
    model: str
    r"""ID of the model to use."""

    inputs: Annotated[ClassificationRequestInputs, pydantic.Field(alias="input")]
    r"""Text to classify."""
