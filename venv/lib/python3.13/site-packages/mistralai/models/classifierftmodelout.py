"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .classifiertargetout import ClassifierTargetOut, ClassifierTargetOutTypedDict
from .ftmodelcapabilitiesout import (
    FTModelCapabilitiesOut,
    FTModelCapabilitiesOutTypedDict,
)
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing import List, Literal, Optional
from typing_extensions import NotRequired, TypedDict


ClassifierFTModelOutObject = Literal["model"]

ClassifierFTModelOutModelType = Literal["classifier"]


class ClassifierFTModelOutTypedDict(TypedDict):
    id: str
    created: int
    owned_by: str
    workspace_id: str
    root: str
    root_version: str
    archived: bool
    capabilities: FTModelCapabilitiesOutTypedDict
    job: str
    classifier_targets: List[ClassifierTargetOutTypedDict]
    object: NotRequired[ClassifierFTModelOutObject]
    name: NotRequired[Nullable[str]]
    description: NotRequired[Nullable[str]]
    max_context_length: NotRequired[int]
    aliases: NotRequired[List[str]]
    model_type: NotRequired[ClassifierFTModelOutModelType]


class ClassifierFTModelOut(BaseModel):
    id: str

    created: int

    owned_by: str

    workspace_id: str

    root: str

    root_version: str

    archived: bool

    capabilities: FTModelCapabilitiesOut

    job: str

    classifier_targets: List[ClassifierTargetOut]

    object: Optional[ClassifierFTModelOutObject] = "model"

    name: OptionalNullable[str] = UNSET

    description: OptionalNullable[str] = UNSET

    max_context_length: Optional[int] = 32768

    aliases: Optional[List[str]] = None

    model_type: Optional[ClassifierFTModelOutModelType] = "classifier"

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "object",
            "name",
            "description",
            "max_context_length",
            "aliases",
            "model_type",
        ]
        nullable_fields = ["name", "description"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
